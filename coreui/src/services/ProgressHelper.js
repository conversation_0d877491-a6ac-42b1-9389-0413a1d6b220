/**
 * Progress Helper Service
 * 
 * This service provides easy-to-use methods for tracking progress of operations
 * using Server-Sent Events (SSE). It can be used in components to show progress
 * for long-running operations like file uploads, data processing, etc.
 */
import { getSSEProgressService } from '../plugins/axios';

class ProgressHelper {
  constructor() {
    this.activeOperations = new Map();
  }

  /**
   * Start tracking progress for a custom operation
   * @param {string} operationId - Unique identifier for the operation
   * @param {string} initialMessage - Initial message to display
   * @returns {Promise} Resolves when progress tracking is started
   */
  async startOperation(operationId, initialMessage = 'Starting operation...') {
    const sseService = getSSEProgressService();
    
    if (!sseService) {
      console.warn('SSE Progress Service not available, falling back to basic progress');
      return;
    }

    // Store operation details
    this.activeOperations.set(operationId, {
      startTime: Date.now(),
      message: initialMessage
    });

    // Start SSE progress tracking
    sseService.startProgress(operationId);
    
    return operationId;
  }

  /**
   * Update progress for an operation
   * @param {string} operationId - Operation identifier
   * @param {number} progress - Progress percentage (0-100)
   * @param {string} message - Progress message
   * @returns {Promise} Resolves when progress is updated
   */
  async updateProgress(operationId, progress, message = '') {
    if (!this.activeOperations.has(operationId)) {
      console.warn(`Operation ${operationId} not found`);
      return;
    }

    // Update local operation data
    const operation = this.activeOperations.get(operationId);
    operation.progress = progress;
    operation.message = message;
    operation.lastUpdate = Date.now();
    
    this.activeOperations.set(operationId, operation);

    // Send update to server
    try {
      await fetch('/api/sse/update-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        },
        body: JSON.stringify({
          requestId: operationId,
          progress,
          message,
          isComplete: progress >= 100
        })
      });
    } catch (error) {
      console.error('Failed to update progress:', error);
    }
  }

  /**
   * Complete an operation
   * @param {string} operationId - Operation identifier
   * @param {string} completionMessage - Final message to display
   * @returns {Promise} Resolves when operation is completed
   */
  async completeOperation(operationId, completionMessage = 'Operation completed') {
    await this.updateProgress(operationId, 100, completionMessage);
    
    // Clean up
    this.activeOperations.delete(operationId);
    
    const sseService = getSSEProgressService();
    if (sseService) {
      sseService.stopProgress(operationId);
    }
  }

  /**
   * Cancel an operation
   * @param {string} operationId - Operation identifier
   * @returns {Promise} Resolves when operation is cancelled
   */
  async cancelOperation(operationId) {
    if (this.activeOperations.has(operationId)) {
      this.activeOperations.delete(operationId);
      
      const sseService = getSSEProgressService();
      if (sseService) {
        sseService.stopProgress(operationId);
      }
    }
  }

  /**
   * Track progress for a file upload
   * @param {File} file - File to upload
   * @param {string} uploadUrl - URL to upload to
   * @param {Object} options - Upload options
   * @returns {Promise} Resolves with upload result
   */
  async trackFileUpload(file, uploadUrl, options = {}) {
    const operationId = `upload_${file.name}_${Date.now()}`;
    
    await this.startOperation(operationId, `Uploading ${file.name}...`);
    
    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append('file', file);
      
      // Add any additional form data
      if (options.data) {
        Object.keys(options.data).forEach(key => {
          formData.append(key, options.data[key]);
        });
      }

      const xhr = new XMLHttpRequest();
      
      // Track upload progress
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = (event.loaded / event.total) * 100;
          this.updateProgress(operationId, progress, `Uploading ${file.name}... ${Math.round(progress)}%`);
        }
      });
      
      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          this.completeOperation(operationId, `${file.name} uploaded successfully`);
          resolve(JSON.parse(xhr.responseText));
        } else {
          this.cancelOperation(operationId);
          reject(new Error(`Upload failed: ${xhr.statusText}`));
        }
      });
      
      xhr.addEventListener('error', () => {
        this.cancelOperation(operationId);
        reject(new Error('Upload failed'));
      });
      
      xhr.open('POST', uploadUrl);
      
      // Add headers
      if (options.headers) {
        Object.keys(options.headers).forEach(key => {
          xhr.setRequestHeader(key, options.headers[key]);
        });
      }
      
      xhr.send(formData);
    });
  }

  /**
   * Track progress for a data processing operation
   * @param {Array} data - Data to process
   * @param {Function} processor - Function to process each item
   * @param {Object} options - Processing options
   * @returns {Promise} Resolves with processed results
   */
  async trackDataProcessing(data, processor, options = {}) {
    const operationId = `process_${Date.now()}`;
    const chunkSize = options.chunkSize || 100;
    const results = [];
    
    await this.startOperation(operationId, 'Processing data...');
    
    try {
      for (let i = 0; i < data.length; i += chunkSize) {
        const chunk = data.slice(i, i + chunkSize);
        const chunkResults = await Promise.all(chunk.map(processor));
        results.push(...chunkResults);
        
        const progress = ((i + chunk.length) / data.length) * 100;
        await this.updateProgress(
          operationId, 
          progress, 
          `Processed ${i + chunk.length} of ${data.length} items`
        );
        
        // Allow UI to update
        await new Promise(resolve => setTimeout(resolve, 1));
      }
      
      await this.completeOperation(operationId, `Processed ${data.length} items successfully`);
      return results;
    } catch (error) {
      await this.cancelOperation(operationId);
      throw error;
    }
  }

  /**
   * Get information about active operations
   * @returns {Array} List of active operations
   */
  getActiveOperations() {
    return Array.from(this.activeOperations.entries()).map(([id, operation]) => ({
      id,
      ...operation
    }));
  }
}

// Create a singleton instance
const progressHelper = new ProgressHelper();

export default progressHelper;
