/**
 * Server-Sent Events (SSE) Progress Service
 *
 * This service manages SSE connections for tracking progress of long-running operations.
 * It connects to the server's SSE endpoint and dispatches progress updates to the store.
 */
class SSEProgressService {
  constructor(store) {
    this.store = store;
    this.eventSource = null;
    this.activeRequestId = null;
    this.requestQueue = new Map(); // Track multiple requests
    this.connected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000; // Start with 1 second delay

    // Initialize the connection
    this.connect().catch(error => {
      console.error('Failed to establish initial SSE connection:', error);
    });
  }

  /**
   * Initialize the SSE connection
   * @returns {Promise} Resolves when connection is established
   */
  connect() {
    return new Promise((resolve, reject) => {
      if (this.connected && this.eventSource) {
        resolve();
        return;
      }

      // Close any existing connection
      this.disconnect();

      try {
        const token = localStorage.getItem("api_token");
        // Create a new EventSource connection to the SSE endpoint
        this.eventSource = new EventSource(`/api/sse/progress?access_token=${token}`);

        // Handle connection open
        this.eventSource.onopen = () => {
          this.connected = true;
          this.reconnectAttempts = 0;
          console.log('SSE connection established');
          resolve();
        };

        // Handle progress events
        this.eventSource.addEventListener('progress', (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleProgressEvent(data);
          } catch (error) {
            console.error('Error parsing SSE progress event:', error);
          }
        });

        // Handle connection events
        this.eventSource.addEventListener('connected', (event) => {
          console.log('SSE server confirmed connection');
        });

        // Handle errors
        this.eventSource.onerror = (error) => {
          console.error('SSE connection error:', error);
          this.connected = false;

          // Attempt to reconnect
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff

            console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

            setTimeout(() => {
              this.connect().catch(err => {
                console.error('Reconnection failed:', err);
              });
            }, delay);
          } else {
            console.error('Max reconnection attempts reached. Giving up.');
            reject(error);
          }
        };
      } catch (error) {
        console.error('Error creating EventSource:', error);
        reject(error);
      }
    });
  }

  /**
   * Disconnect from the SSE endpoint
   */
  disconnect() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    this.connected = false;
  }

  /**
   * Start tracking progress for a request
   * @param {string} requestId - Identifier for the request
   */
  startProgress(requestId) {
    // Add to request queue
    this.requestQueue.set(requestId, {
      startTime: Date.now(),
      progress: 0,
      message: 'Starting...'
    });

    this.activeRequestId = requestId;

    // Ensure we're connected to SSE
    this.connect().then(() => {
      // Notify the server about the new request
      fetch(`/api/sse/register-progress`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        },
        body: JSON.stringify({ requestId })
      }).catch(error => {
        console.error('Failed to register progress tracking:', error);
      });

      // Show progress bar with initial state
      this.store.dispatch("app/updateProgress", {
        progress: 0,
        message: 'Starting request...',
        isComplete: false
      });
      this.store.dispatch("app/setProgressBar", true);
    }).catch(error => {
      console.error('Failed to connect to SSE:', error);
      // Fallback to regular progress bar
      this.store.dispatch("app/loadProgressBar");
    });
  }

  /**
   * Stop tracking progress for the current request or a specific request
   * @param {string} [requestId] - Optional specific request ID to stop
   */
  stopProgress(requestId = null) {
    const idToStop = requestId || this.activeRequestId;

    if (idToStop) {
      // Remove from request queue
      this.requestQueue.delete(idToStop);

      // Notify the server that we're done with this request
      fetch(`/api/sse/unregister-progress`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        },
        body: JSON.stringify({ requestId: idToStop })
      }).catch(error => {
        console.error('Failed to unregister progress tracking:', error);
      });

      // If we're stopping the active request, clear it and update UI
      if (idToStop === this.activeRequestId) {
        this.activeRequestId = null;

        // If there are other requests in the queue, activate the most recent one
        if (this.requestQueue.size > 0) {
          const entries = Array.from(this.requestQueue.entries());
          const [nextRequestId, nextRequest] = entries[entries.length - 1];
          this.activeRequestId = nextRequestId;

          // Update progress for the new active request
          this.store.dispatch("app/updateProgress", {
            progress: nextRequest.progress || 0,
            message: nextRequest.message || 'Processing...',
            isComplete: false
          });
        } else {
          // No more requests, hide progress bar
          this.store.dispatch("app/setProgressBar", false);
        }
      }
    }
  }

  /**
   * Handle progress events from the server
   * @param {Object} data - Progress event data
   */
  handleProgressEvent(data) {
    // Update the request in our queue
    if (data.requestId && this.requestQueue.has(data.requestId)) {
      const request = this.requestQueue.get(data.requestId);
      request.progress = data.progress || 0;
      request.message = data.message || '';
      request.isComplete = data.isComplete || false;

      this.requestQueue.set(data.requestId, request);

      // If this is for our active request, update the UI
      if (data.requestId === this.activeRequestId) {
        this.store.dispatch("app/updateProgress", {
          progress: data.progress,
          message: data.message || '',
          isComplete: data.isComplete || false
        });

        // If the operation is complete, stop tracking
        if (data.isComplete) {
          this.stopProgress(data.requestId);
        }
      }
    }
  }
}

export default SSEProgressService;
