import axios from 'axios'
import {storePromise} from '../store';

window.axios = axios

// SSE Progress Service will be initialized after store is ready
let sseProgressService = null;

storePromise.then(async store => {
  // Dynamically import SSE service to avoid circular dependencies
  const { default: SSEProgressService } = await import('../services/SSEProgressService');
  sseProgressService = new SSEProgressService(store);

  // Modified axios interceptors to work with SSE
  axios.interceptors.request.use(
    (config) => {
      // Check if this request should use SSE progress tracking
      if (config.useSSEProgress !== false && sseProgressService) {
        // Generate a unique request ID
        const requestId = `${config.method}_${config.url}_${Date.now()}`;
        config.requestId = requestId;

        // Start SSE progress tracking for this request
        sseProgressService.startProgress(requestId);
      } else {
        // Fallback to old progress bar for specific requests
        store.dispatch("app/loadProgressBar");
      }
      return config;
    },
    (error) => {
      if (sseProgressService) {
        sseProgressService.stopProgress();
      }
      store.dispatch("app/unLoadProgressBar");
      return Promise.reject(error);
    }
  );

  axios.interceptors.response.use(
    (response) => {
      if (sseProgressService && response.config.requestId) {
        sseProgressService.stopProgress();
      } else {
        store.dispatch("app/unLoadProgressBar");
      }
      return response;
    },
    (error) => {
      if (sseProgressService) {
        sseProgressService.stopProgress();
      }
      store.dispatch("app/unLoadProgressBar");
      return Promise.reject(error);
    }
  );
})

// Function to get SSE service instance
export const getSSEProgressService = () => sseProgressService;

