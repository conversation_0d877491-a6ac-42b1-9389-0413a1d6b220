<template>
  <div class="sse-progress-example">
    <v-card>
      <v-card-title>
        <v-icon left>mdi-progress-clock</v-icon>
        SSE Progress Tracking Examples
      </v-card-title>
      
      <v-card-text>
        <p class="mb-4">
          This component demonstrates how to use Server-Sent Events (SSE) for progress tracking
          instead of the traditional axios interceptors.
        </p>

        <!-- Current Progress Display -->
        <v-alert
          v-if="isProgressActive"
          type="info"
          outlined
          class="mb-4"
        >
          <div class="d-flex align-center">
            <v-progress-circular
              :value="currentProgress"
              :indeterminate="currentProgress === 0"
              size="24"
              width="3"
              color="primary"
              class="mr-3"
            />
            <div>
              <div class="font-weight-medium">{{ currentProgressMessage }}</div>
              <div class="text-caption" v-if="currentProgress > 0">
                {{ Math.round(currentProgress) }}% complete
              </div>
            </div>
          </div>
        </v-alert>

        <!-- Example Buttons -->
        <div class="d-flex flex-wrap gap-3 mb-4">
          <v-btn
            color="primary"
            @click="simulateProgress"
            :disabled="isProgressActive"
          >
            <v-icon left>mdi-play</v-icon>
            Simulate Progress
          </v-btn>

          <v-btn
            color="secondary"
            @click="simulateFileUpload"
            :disabled="isProgressActive"
          >
            <v-icon left>mdi-upload</v-icon>
            Simulate File Upload
          </v-btn>

          <v-btn
            color="success"
            @click="simulateDataProcessing"
            :disabled="isProgressActive"
          >
            <v-icon left>mdi-database</v-icon>
            Simulate Data Processing
          </v-btn>

          <v-btn
            color="warning"
            @click="makeAPIRequest"
            :disabled="isProgressActive"
          >
            <v-icon left>mdi-api</v-icon>
            Make API Request
          </v-btn>

          <v-btn
            color="error"
            @click="cancelProgress"
            :disabled="!isProgressActive"
          >
            <v-icon left>mdi-stop</v-icon>
            Cancel
          </v-btn>
        </div>

        <!-- File Upload Example -->
        <v-divider class="my-4" />
        <h3 class="mb-3">File Upload with Progress</h3>
        <v-file-input
          v-model="selectedFile"
          label="Select a file to upload"
          outlined
          dense
          :disabled="isProgressActive"
        />
        <v-btn
          color="primary"
          @click="uploadFile"
          :disabled="!selectedFile || isProgressActive"
          class="mb-4"
        >
          <v-icon left>mdi-cloud-upload</v-icon>
          Upload File
        </v-btn>

        <!-- Progress History -->
        <v-divider class="my-4" />
        <h3 class="mb-3">Progress History</h3>
        <v-list dense>
          <v-list-item
            v-for="(operation, index) in progressHistory"
            :key="index"
            class="px-0"
          >
            <v-list-item-content>
              <v-list-item-title>{{ operation.name }}</v-list-item-title>
              <v-list-item-subtitle>
                {{ operation.message }} - {{ operation.timestamp }}
              </v-list-item-subtitle>
            </v-list-item-content>
            <v-list-item-action>
              <v-chip
                :color="operation.status === 'completed' ? 'success' : operation.status === 'failed' ? 'error' : 'warning'"
                small
                text-color="white"
              >
                {{ operation.status }}
              </v-chip>
            </v-list-item-action>
          </v-list-item>
        </v-list>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import SSEProgressMixin from '../../mixins/SSEProgressMixin';

export default {
  name: 'SSEProgressExample',
  mixins: [SSEProgressMixin],
  
  data() {
    return {
      selectedFile: null,
      progressHistory: []
    };
  },

  methods: {
    async simulateProgress() {
      try {
        this.addToHistory('Progress Simulation', 'started');
        await this.simulateProgress(3000, 'simulation');
        this.addToHistory('Progress Simulation', 'completed');
      } catch (error) {
        this.addToHistory('Progress Simulation', 'failed');
        console.error('Simulation failed:', error);
      }
    },

    async simulateFileUpload() {
      try {
        this.addToHistory('File Upload Simulation', 'started');
        
        // Create a fake file for demonstration
        const fakeFile = new File(['fake content'], 'demo.txt', { type: 'text/plain' });
        
        // Simulate upload with progress
        await this.uploadFileWithProgress(fakeFile, '/api/fake-upload', {
          data: { description: 'Demo upload' }
        });
        
        this.addToHistory('File Upload Simulation', 'completed');
      } catch (error) {
        this.addToHistory('File Upload Simulation', 'failed');
        console.error('Upload simulation failed:', error);
      }
    },

    async simulateDataProcessing() {
      try {
        this.addToHistory('Data Processing', 'started');
        
        // Create fake data to process
        const data = Array.from({ length: 100 }, (_, i) => ({ id: i, value: Math.random() }));
        
        // Process with progress tracking
        const results = await this.processDataWithProgress(
          data,
          async (item) => {
            // Simulate processing time
            await new Promise(resolve => setTimeout(resolve, 50));
            return { ...item, processed: true };
          },
          { chunkSize: 10 }
        );
        
        console.log('Processing results:', results);
        this.addToHistory('Data Processing', 'completed');
      } catch (error) {
        this.addToHistory('Data Processing', 'failed');
        console.error('Data processing failed:', error);
      }
    },

    async makeAPIRequest() {
      try {
        this.addToHistory('API Request', 'started');
        
        // Make a request with automatic progress tracking
        const response = await this.requestWithProgress({
          method: 'GET',
          url: '/api/some-endpoint'
        });
        
        console.log('API Response:', response);
        this.addToHistory('API Request', 'completed');
      } catch (error) {
        this.addToHistory('API Request', 'failed');
        console.error('API request failed:', error);
      }
    },

    async uploadFile() {
      if (!this.selectedFile) return;

      try {
        this.addToHistory(`Upload: ${this.selectedFile.name}`, 'started');
        
        const result = await this.uploadFileWithProgress(
          this.selectedFile,
          '/api/upload',
          {
            data: { type: 'user_upload' }
          }
        );
        
        console.log('Upload result:', result);
        this.addToHistory(`Upload: ${this.selectedFile.name}`, 'completed');
        this.selectedFile = null;
      } catch (error) {
        this.addToHistory(`Upload: ${this.selectedFile.name}`, 'failed');
        console.error('File upload failed:', error);
      }
    },

    addToHistory(name, status) {
      this.progressHistory.unshift({
        name,
        status,
        message: `Operation ${status}`,
        timestamp: new Date().toLocaleTimeString()
      });
      
      // Keep only last 10 entries
      if (this.progressHistory.length > 10) {
        this.progressHistory = this.progressHistory.slice(0, 10);
      }
    }
  }
};
</script>

<style scoped>
.sse-progress-example {
  max-width: 800px;
  margin: 0 auto;
}

.gap-3 > * {
  margin-right: 12px;
  margin-bottom: 8px;
}
</style>
