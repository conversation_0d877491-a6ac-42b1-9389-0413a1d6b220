<template>
  <v-dialog
    v-model="dialog"
    :max-width="options.width"
    :style="{ zIndex: options.zIndex }"
    persistent
  >
    <v-card>
      <v-toolbar dark :color="options.color" dense flat>
        <v-toolbar-title class="white--text">
          {{ progressData.message || 'Loading...' }}
        </v-toolbar-title>
      </v-toolbar>
      <v-card-actions class="pt-5">
        <v-spacer></v-spacer>
        <v-progress-linear
          :value="progressData.progress"
          :indeterminate="progressData.progress === 0"
          color="cyan"
          height="6"
        >
          <template v-slot:default="{ value }">
            <strong v-if="progressData.progress > 0">{{ Math.ceil(value) }}%</strong>
          </template>
        </v-progress-linear>
      </v-card-actions>
      <v-card-text v-if="progressData.progress > 0" class="text-center">
        <small class="text--secondary">{{ Math.ceil(progressData.progress) }}% complete</small>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>


<script>
import { mapState, mapActions } from "vuex";
export default {
  data: () => ({
    dialog: false,
    resolve: null,
    reject: null,
    options: {
      color: "primary",
      width: 350, // Increased width to accommodate progress text
      zIndex: 1000000000,
    },
  }),
  computed:{
    ...mapState("app", ["progressBar", "progressData"])
  },
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open(options) {
      this.loadVApp()
      this.dialog = true;
      this.options = Object.assign(this.options, options);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      })
    },
    agree() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp()
    },
    cancel() {
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp()
    },
  },
  watch:{
    progressBar(newVal) {
      this.dialog = newVal
      if(newVal) {
        this.loadVApp()
        return
      }
      this.unLoadVApp()
    }
  },
};
</script>
