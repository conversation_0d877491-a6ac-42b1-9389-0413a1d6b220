const reportsData = [
    {
        id: "general",
        title: "General",
        icon: "cil-settings",
        permission: "show_reports_general",
        items: [
            { title: "Structure", route: "structure-report", permission: "show_reports_structure" },
            { title: "Product", route: "product-report", permission: "show_reports_product" },
            { title: "Log Activity", route: "LogActivity-report", permission: "show_reports_log_activity" },
            { title: "List Report", route: "list-report", permission: "show_reports_list_report" },
            { title: "List Statistics", route: "list-statistics-report", permission: "show_reports_list_statistics" },
            { title: "List Per Speciality Report", route: "list-per-speciality-report", permission: "show_reports_list_statistics" },
            { title: "Employee Tracking", route: "employee-tracking-report", permission: "show_reports_employee_tracking" },
            { title: "Employee Kpis", route: "employee-kpis-report", permission: "show_reports_employee_kpis" },
            { title: "Working Hours", route: "working-hours-report", permission: "show_reports_working_hours" },
            { title: "Kpis Policy", route: "kpis-policy-report", permission: "show_reports_kpis_policy" },
            { title: "Employee Performance", route: "employee-performance-report", permission: "show_reports_employee_performance" },
            { title: "Doctor Profiling", route: "doctor-profiling-report", permission: "show_reports_doctor_profiling" },
            { title: "Linked Pharmacies Statistics", route: "linked-pharmacies-statistics-report", permission: "show_reports_linked_pharmacies_statistics" },
            { title: "Linked Pharmacies Details", route: "linked-pharmacies-details-report", permission: "show_reports_linked_pharmacies_details" },
            { title: "Notification", route: "notifications-report", permission: "show_reports_notifications" },
            { title: "PV", route: "pv-report", permission: "show_reports_pv" }
        ]
    },
    {
        id: "sales",
        title: "Sales",
        icon: "cil-dollar",
        permission: "show_reports_sales",
        items: [
            { title: "Mapping", route: "mapping-report", permission: "show_reports_mapping" },
            { title: "Error Mapping", route: "error-mapping-report", permission: "show_error_reports_mapping" },
            { title: "Product Mapping", route: "product-mapping-report", permission: "show_reports_product_mapping" },
            { title: "Sales Details", route: "sales-details", permission: "show_reports_sales_details" },
            { title: "Branch Sales Details", route: "branch-sales-details", permission: "show_reports_branch_sales_details" },
            { title: "Sales Summary", route: "sales-summery", permission: "show_reports_sales_summary" },
            { title: "Sales Per Distributor", route: "sales-per-distributors", permission: "show_reports_sales_per_distributors" },
            { title: "Sales Achievement", route: "sales-achievement", permission: "show_reports_sales_achievement" },
            { title: "Target Summary", route: "target-summary", permission: "show_reports_target_summary" },
            { title: "Target Details", route: "target-details-report", permission: "show_reports_target_details" },
            { title: "Sales Summary Unified", route: "sales-summary-unified-report", permission: "show_reports_sales_summary_unified" },
            { title: "Sales Ceiling", route: "ceiling-report", permission: "show_reports_sales_summary_unified" },
            { title: "Unified Sales Details", route: "unified-sales-details-report", permission: "show_reports_unified_sales" },
            { title: "Sales Incentive", route: "sales-incentive-report", permission: "show_reports_sales_incentive" },
            { title: "Sales Achievement Comparison", route: "sales-achievement-comparison-report", permission: "show_sales-achievement-comparison" }
        ]
    },
    {
        id: "visits",
        title: "Visits",
        icon: "cilColumns",
        permission: "show_reports_visits",
        items: [
            { title: "Overall Visits", route: "visits-report", permission: "show_reports_overall_visits" },
            { title: "Visits Statistics", route: "visitsStatistics", permission: "show_reports_visits_statistics" },
            { title: "Coverage", route: "coverage-report", permission: "show_reports_coverage" },
            { title: "Call Rate", route: "call-rate-report", permission: "show_reports_call_rate" },
            { title: "Frequency", route: "frequency-report", permission: "show_reports_frequency" },
            { title: "Start Point Report", route: "start-point-report", permission: "show_reports_start_point" },
            { title: "Detailing Statistics", route: "detailing-statistics-report", permission: "show_reports_detailing_statistics" },
            { title: "Managers", route: "managers-report", permission: "show_reports_managers" },
            { title: "Product Calls", route: "product-frequency-report", permission: "show_reports_product_frequency" },
            { title: "Speciality Visits Statistics", route: "speciality-statistics-report", permission: "show_reports_speciality_statistics" },
            { title: "Doctor Tracing", route: "doctor-tracing-report", permission: "show_reports_doctor_tracing" },
            { title: "Customer Visits", route: "customer-visits-report", permission: "show_reports_customer_visits" },
            { title: "Samples Consumption", route: "samples-consumption-report", permission: "show_reports_samples_consumption" },
            { title: "Giveaways Consumption", route: "giveaways-consumption-report", permission: "show_reports_giveaways_consumption" }
        ]
    },
    {
        id: "requests",
        title: "Requests",
        icon: "cil-description",
        permission: "show_reports_request",
        items: [
            { title: "Vacation Statistics", route: "vacation-statistics-report", permission: "show_reports_vacation_statistics" },
            { title: "Overall Vacations", route: "vacation-report", permission: "show_reports_vacation_details" },
            { title: "Expense Statistics", route: "expense-statistics", permission: "show_reports_expense_statistics" },
            { title: "Expense Statistics V2", route: "expense-statistics-second-version", permission: "show_reports_expense_statistics_v2" },
            { title: "Overall Expenses", route: "expense", permission: "show_reports_expense_details" },
            { title: "Commercial Statistics", route: "commercial-statistics", permission: "show_reports_commercial_statistics" },
            { title: "Overall Commercials", route: "commercial-details", permission: "show_reports_commercial_details" },
            { title: "Commercial Bills", route: "commercial-bills", permission: "show_reports_commercial_bills" },
            { title: "Commercial Costs", route: "commercial-costs", permission: "show_reports_commercial_Costs" },
            { title: "ROI Trend", route: "roi-trend", permission: "show_reports_roi_trend" },
            { title: "End Commercial Payments", route: "end-payment", permission: "show_reports_end_commercial_payments" },
            { title: "Finance Requests Summary", route: "finance-summery", permission: "show_reports_finance_summary" },
            { title: "Custody", route: "custody-report", permission: "show_reports_custody" },
            { title: "Overall Budgets", route: "budget-report", permission: "show_reports_budget_details" },
            { title: "Budget Consumptions", route: "budget-consumptions", permission: "show_reports_budget_consumptions" },
            { title: "Budget Analysis", route: "budget-analysis", permission: "show_reports_budget_analysis" },
            { title: "Material Statistics", route: "material-statistics", permission: "show_reports_material_statistics" },
            { title: "Overall materials", route: "material-report", permission: "show_reports_material_details" },
            { title: "Material Costs", route: "material-costs", permission: "show_reports_material_costs" },
            { title: "Order Request", route: "order-requests", permission: "show_reports_order_requests" },
            { title: "Finance Order", route: "finance-orders", permission: "show_reports_finance_orders" }
        ]
    },
    {
        id: "gps",
        title: "GPS",
        icon: "cil-location-pin",
        permission: "show_reports_gps",
        items: [
            { title: "Employee Route", route: "employee-route", permission: "show_reports_employee_route" },
            { title: "Out Of Location", route: "out-of-location", permission: "show_reports_out_of_locations" },
            { title: "Brick Mapping Positions", route: "brick-mapping-positions", permission: "show_reports_out_of_locations" },
            { title: "User Tracking", route: "employee-tracking-map", permission: "show_reports_out_of_locations" },
            { title: "Overall Visits", route: "gps-visits", permission: "show_gps_visits" },
            { title: "Live Location", route: "Locations", permission: "show_gps_visits" },
            { title: "Location Discrepancy Analysis", route: "location-discrepancy-analysis", permission: "show_reports_location_discrepancy" }
        ]
    },
    {
        id: "coaching",
        title: "Training",
        icon: "cil-list",
        permission: "show_reports_coaching",
        items: [
            { title: "Coaching & Performance Summary", route: "coaching-performance-summary-report", permission: "show_reports_coaching_performance_summary" },
            { title: "Coaching Statistics", route: "coaching-statistics-report", permission: "show_reports_coaching_statistics" },
            { title: "Question Quiz", route: "training-category-report", permission: "show_reports_question_quiz" },
            { title: "Quiz Result Summary", route: "quiz-result-summary-report", permission: "show_reports_quiz_summery" }
        ]
    }
];

export default reportsData;