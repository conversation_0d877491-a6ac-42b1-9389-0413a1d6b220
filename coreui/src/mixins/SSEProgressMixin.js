/**
 * SSE Progress Mixin
 * 
 * This mixin provides easy access to SSE progress tracking functionality
 * in Vue components. It includes methods for starting, updating, and completing
 * progress operations.
 */
import ProgressHelper from '../services/ProgressHelper';
import { mapState } from 'vuex';

export default {
  data() {
    return {
      // Local progress tracking
      localProgress: {
        active: false,
        progress: 0,
        message: '',
        operationId: null
      }
    };
  },

  computed: {
    ...mapState('app', ['progressBar', 'progressData']),
    
    /**
     * Check if any progress operation is active
     */
    isProgressActive() {
      return this.progressBar || this.localProgress.active;
    },
    
    /**
     * Get current progress percentage
     */
    currentProgress() {
      return this.localProgress.active ? this.localProgress.progress : this.progressData.progress;
    },
    
    /**
     * Get current progress message
     */
    currentProgressMessage() {
      return this.localProgress.active ? this.localProgress.message : this.progressData.message;
    }
  },

  methods: {
    /**
     * Start a progress operation
     * @param {string} operationName - Name of the operation
     * @param {string} initialMessage - Initial message to display
     * @returns {Promise<string>} Operation ID
     */
    async startProgress(operationName, initialMessage = 'Starting...') {
      const operationId = `${operationName}_${this.$options.name || 'component'}_${Date.now()}`;
      
      try {
        await ProgressHelper.startOperation(operationId, initialMessage);
        
        // Update local state
        this.localProgress = {
          active: true,
          progress: 0,
          message: initialMessage,
          operationId
        };
        
        return operationId;
      } catch (error) {
        console.error('Failed to start progress:', error);
        throw error;
      }
    },

    /**
     * Update progress for the current operation
     * @param {number} progress - Progress percentage (0-100)
     * @param {string} message - Progress message
     * @returns {Promise}
     */
    async updateProgress(progress, message = '') {
      if (!this.localProgress.operationId) {
        console.warn('No active progress operation to update');
        return;
      }

      try {
        await ProgressHelper.updateProgress(this.localProgress.operationId, progress, message);
        
        // Update local state
        this.localProgress.progress = progress;
        this.localProgress.message = message;
      } catch (error) {
        console.error('Failed to update progress:', error);
      }
    },

    /**
     * Complete the current progress operation
     * @param {string} completionMessage - Final message to display
     * @returns {Promise}
     */
    async completeProgress(completionMessage = 'Operation completed') {
      if (!this.localProgress.operationId) {
        console.warn('No active progress operation to complete');
        return;
      }

      try {
        await ProgressHelper.completeOperation(this.localProgress.operationId, completionMessage);
        
        // Reset local state
        this.localProgress = {
          active: false,
          progress: 0,
          message: '',
          operationId: null
        };
      } catch (error) {
        console.error('Failed to complete progress:', error);
      }
    },

    /**
     * Cancel the current progress operation
     * @returns {Promise}
     */
    async cancelProgress() {
      if (!this.localProgress.operationId) {
        return;
      }

      try {
        await ProgressHelper.cancelOperation(this.localProgress.operationId);
        
        // Reset local state
        this.localProgress = {
          active: false,
          progress: 0,
          message: '',
          operationId: null
        };
      } catch (error) {
        console.error('Failed to cancel progress:', error);
      }
    },

    /**
     * Track file upload with progress
     * @param {File} file - File to upload
     * @param {string} uploadUrl - URL to upload to
     * @param {Object} options - Upload options
     * @returns {Promise} Upload result
     */
    async uploadFileWithProgress(file, uploadUrl, options = {}) {
      try {
        const result = await ProgressHelper.trackFileUpload(file, uploadUrl, options);
        return result;
      } catch (error) {
        console.error('File upload failed:', error);
        throw error;
      }
    },

    /**
     * Process data with progress tracking
     * @param {Array} data - Data to process
     * @param {Function} processor - Function to process each item
     * @param {Object} options - Processing options
     * @returns {Promise} Processing results
     */
    async processDataWithProgress(data, processor, options = {}) {
      try {
        const results = await ProgressHelper.trackDataProcessing(data, processor, options);
        return results;
      } catch (error) {
        console.error('Data processing failed:', error);
        throw error;
      }
    },

    /**
     * Make an HTTP request with automatic progress tracking
     * @param {Object} config - Axios config object
     * @returns {Promise} Request result
     */
    async requestWithProgress(config) {
      // Ensure SSE progress is enabled for this request
      const requestConfig = {
        ...config,
        useSSEProgress: true
      };

      try {
        const response = await this.$http(requestConfig);
        return response;
      } catch (error) {
        console.error('Request failed:', error);
        throw error;
      }
    },

    /**
     * Simulate progress for demonstration purposes
     * @param {number} duration - Duration in milliseconds
     * @param {string} operationName - Name of the operation
     * @returns {Promise}
     */
    async simulateProgress(duration = 5000, operationName = 'demo') {
      const operationId = await this.startProgress(operationName, 'Starting simulation...');
      
      const steps = 20;
      const stepDuration = duration / steps;
      
      for (let i = 1; i <= steps; i++) {
        await new Promise(resolve => setTimeout(resolve, stepDuration));
        const progress = (i / steps) * 100;
        await this.updateProgress(progress, `Step ${i} of ${steps} completed`);
      }
      
      await this.completeProgress('Simulation completed successfully');
    }
  },

  beforeDestroy() {
    // Clean up any active progress operations when component is destroyed
    if (this.localProgress.operationId) {
      this.cancelProgress().catch(error => {
        console.error('Failed to cancel progress on component destroy:', error);
      });
    }
  }
};
